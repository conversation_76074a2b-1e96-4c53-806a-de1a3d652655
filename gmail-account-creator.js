const adb = require('adbkit');
const fs = require('fs-extra');
const path = require('path');

class GmailAccountCreator {
    constructor() {
        this.client = adb.createClient();
        this.device = null;
        this.logFile = 'run.log';
        this.accountsFile = 'accounts.json';
        this.maxRetries = 3;
        this.screenWidth = 1080;
        this.screenHeight = 1920;
    }

    // Logging utility
    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level}] ${message}\n`;
        console.log(logEntry.trim());
        fs.appendFileSync(this.logFile, logEntry);
    }

    // Random delay for human-like behavior
    async randomDelay(min = 500, max = 2500) {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        this.log(`Waiting ${delay}ms for human-like delay`);
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Random tap with coordinate variation
    async randomTap(x, y, variation = 10) {
        const randomX = x + (Math.random() - 0.5) * variation * 2;
        const randomY = y + (Math.random() - 0.5) * variation * 2;
        
        const finalX = Math.max(0, Math.min(this.screenWidth, Math.floor(randomX)));
        const finalY = Math.max(0, Math.min(this.screenHeight, Math.floor(randomY)));
        
        this.log(`Tapping at coordinates (${finalX}, ${finalY})`);
        await this.device.shell(`input tap ${finalX} ${finalY}`);
        await this.randomDelay(200, 800);
    }

    // Generate random account data
    generateAccountData() {
        const firstNames = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', 'Riley', 'Avery', 'Quinn', 'Sage', 'River'];
        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
        const domains = ['gmail.com'];
        
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        const randomNum = Math.floor(Math.random() * 9999) + 1000;
        const year = Math.floor(Math.random() * 30) + 1980; // 1980-2009
        const month = Math.floor(Math.random() * 12) + 1;
        const day = Math.floor(Math.random() * 28) + 1;
        
        const username = `${firstName.toLowerCase()}${lastName.toLowerCase()}${randomNum}`;
        const password = this.generateSecurePassword();
        
        return {
            firstName,
            lastName,
            username,
            email: `${username}@gmail.com`,
            password,
            birthYear: year,
            birthMonth: month,
            birthDay: day,
            gender: Math.random() > 0.5 ? 'Male' : 'Female',
            recoveryEmail: Math.random() > 0.3 ? `${username}<EMAIL>` : null,
            createdAt: new Date().toISOString()
        };
    }

    // Generate secure password
    generateSecurePassword() {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        for (let i = 0; i < 12; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
    }

    // Type text with human-like behavior (typos, corrections)
    async humanTypeText(text, includeTypos = true) {
        this.log(`Typing text: ${text}`);
        
        if (includeTypos && Math.random() > 0.7) {
            // Simulate typo
            const typoText = text + 'x';
            await this.device.shell(`input text "${typoText}"`);
            await this.randomDelay(300, 800);
            
            // Backspace to correct
            await this.device.shell('input keyevent KEYCODE_DEL');
            await this.randomDelay(200, 500);
        } else {
            await this.device.shell(`input text "${text}"`);
        }
        
        await this.randomDelay(300, 1000);
    }

    // Change device settings for fingerprint variation
    async randomizeDeviceSettings() {
        this.log('Randomizing device settings for anti-detection');
        
        const languages = ['en-US', 'en-GB', 'en-CA', 'en-AU'];
        const timezones = ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver'];
        const densities = ['320', '480', '640'];
        
        const selectedLang = languages[Math.floor(Math.random() * languages.length)];
        const selectedTz = timezones[Math.floor(Math.random() * timezones.length)];
        const selectedDensity = densities[Math.floor(Math.random() * densities.length)];
        
        try {
            await this.device.shell(`settings put system system_locales ${selectedLang}`);
            await this.device.shell(`setprop persist.sys.timezone ${selectedTz}`);
            await this.device.shell(`settings put secure display_density_forced ${selectedDensity}`);
            
            this.log(`Set language: ${selectedLang}, timezone: ${selectedTz}, density: ${selectedDensity}`);
        } catch (error) {
            this.log(`Error setting device properties: ${error.message}`, 'WARN');
        }
    }

    // Get UI dump for state verification
    async getUiDump() {
        try {
            const result = await this.device.shell('uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml');
            return result.toString();
        } catch (error) {
            this.log(`Error getting UI dump: ${error.message}`, 'ERROR');
            return null;
        }
    }

    // Check if element exists in UI
    async elementExists(text, attribute = 'text') {
        const uiDump = await this.getUiDump();
        if (!uiDump) return false;
        
        const regex = new RegExp(`${attribute}="[^"]*${text}[^"]*"`, 'i');
        return regex.test(uiDump);
    }

    // Find element coordinates
    async findElementCoordinates(text, attribute = 'text') {
        const uiDump = await this.getUiDump();
        if (!uiDump) return null;
        
        const regex = new RegExp(`${attribute}="[^"]*${text}[^"]*"[^>]*bounds="\\[(\\d+),(\\d+)\\]\\[(\\d+),(\\d+)\\]"`, 'i');
        const match = uiDump.match(regex);
        
        if (match) {
            const x1 = parseInt(match[1]);
            const y1 = parseInt(match[2]);
            const x2 = parseInt(match[3]);
            const y2 = parseInt(match[4]);
            
            return {
                x: Math.floor((x1 + x2) / 2),
                y: Math.floor((y1 + y2) / 2)
            };
        }
        
        return null;
    }

    // Launch Gmail app
    async launchGmail() {
        this.log('Launching Gmail app');
        
        try {
            // Try to launch directly to add account flow
            await this.device.shell('am start -n com.google.android.gm/.ConversationListActivityGmail');
            await this.randomDelay(3000, 5000);
            
            // Check if we need to navigate to add account
            if (await this.elementExists('Add account')) {
                const coords = await this.findElementCoordinates('Add account');
                if (coords) {
                    await this.randomTap(coords.x, coords.y);
                    return true;
                }
            }
            
            // Alternative: try settings approach
            await this.device.shell('input keyevent KEYCODE_MENU');
            await this.randomDelay(1000, 2000);
            
            if (await this.elementExists('Settings')) {
                const coords = await this.findElementCoordinates('Settings');
                if (coords) {
                    await this.randomTap(coords.x, coords.y);
                    await this.randomDelay(2000, 3000);
                }
            }
            
            return true;
        } catch (error) {
            this.log(`Error launching Gmail: ${error.message}`, 'ERROR');
            return false;
        }
    }

    // Navigate through account creation flow
    async navigateAccountCreation(accountData) {
        this.log('Navigating account creation flow');

        // Step 1: Look for "Create account" or "Add account"
        await this.randomDelay(2000, 4000);

        if (await this.elementExists('Create account')) {
            const coords = await this.findElementCoordinates('Create account');
            if (coords) await this.randomTap(coords.x, coords.y);
        } else if (await this.elementExists('Add account')) {
            const coords = await this.findElementCoordinates('Add account');
            if (coords) await this.randomTap(coords.x, coords.y);
        }

        await this.randomDelay(2000, 3000);

        // Step 2: Select Google account type
        if (await this.elementExists('Google')) {
            const coords = await this.findElementCoordinates('Google');
            if (coords) await this.randomTap(coords.x, coords.y);
        }

        await this.randomDelay(2000, 3000);

        // Step 3: Choose "Create account"
        if (await this.elementExists('Create account')) {
            const coords = await this.findElementCoordinates('Create account');
            if (coords) await this.randomTap(coords.x, coords.y);
        }

        await this.randomDelay(2000, 3000);

        // Step 4: Fill in first name
        if (await this.elementExists('First name')) {
            const coords = await this.findElementCoordinates('First name');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.firstName);
            }
        }

        // Step 5: Fill in last name
        if (await this.elementExists('Last name')) {
            const coords = await this.findElementCoordinates('Last name');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.lastName);
            }
        }

        // Step 6: Continue to next step
        await this.tapNextButton();

        // Step 7: Fill username (if prompted to choose)
        await this.randomDelay(2000, 3000);
        if (await this.elementExists('Choose your username')) {
            const coords = await this.findElementCoordinates('username', 'hint');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.username);
            }
        }

        await this.tapNextButton();

        // Step 8: Set password
        await this.randomDelay(2000, 3000);
        if (await this.elementExists('password', 'hint')) {
            const coords = await this.findElementCoordinates('password', 'hint');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.password);
            }
        }

        // Confirm password
        if (await this.elementExists('Confirm')) {
            const coords = await this.findElementCoordinates('Confirm');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.password);
            }
        }

        await this.tapNextButton();

        // Step 9: Handle phone number (skip or add recovery)
        await this.randomDelay(2000, 3000);
        if (Math.random() > 0.5) {
            // Skip phone number
            if (await this.elementExists('Skip')) {
                const coords = await this.findElementCoordinates('Skip');
                if (coords) await this.randomTap(coords.x, coords.y);
            }
        } else {
            // Add fake phone number (this might fail, which is expected)
            const fakePhone = `555${Math.floor(Math.random() * 9000000) + 1000000}`;
            if (await this.elementExists('phone', 'hint')) {
                const coords = await this.findElementCoordinates('phone', 'hint');
                if (coords) {
                    await this.randomTap(coords.x, coords.y);
                    await this.humanTypeText(fakePhone);
                }
            }
        }

        await this.tapNextButton();

        // Step 10: Set birthday and gender
        await this.randomDelay(2000, 3000);
        await this.fillBirthdayAndGender(accountData);

        await this.tapNextButton();

        // Step 11: Accept terms
        await this.randomDelay(2000, 3000);
        if (await this.elementExists('I agree')) {
            const coords = await this.findElementCoordinates('I agree');
            if (coords) await this.randomTap(coords.x, coords.y);
        }

        // Wait for account creation to complete
        await this.randomDelay(5000, 10000);

        this.log('Account creation flow completed');
    }

    // Helper method to tap Next button
    async tapNextButton() {
        await this.randomDelay(1000, 2000);
        if (await this.elementExists('Next')) {
            const coords = await this.findElementCoordinates('Next');
            if (coords) await this.randomTap(coords.x, coords.y);
        }
        await this.randomDelay(2000, 3000);
    }

    // Fill birthday and gender information
    async fillBirthdayAndGender(accountData) {
        // Set month
        if (await this.elementExists('Month')) {
            const coords = await this.findElementCoordinates('Month');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.randomDelay(500, 1000);

                // Select month from dropdown
                const months = ['January', 'February', 'March', 'April', 'May', 'June',
                              'July', 'August', 'September', 'October', 'November', 'December'];
                const monthName = months[accountData.birthMonth - 1];

                if (await this.elementExists(monthName)) {
                    const monthCoords = await this.findElementCoordinates(monthName);
                    if (monthCoords) await this.randomTap(monthCoords.x, monthCoords.y);
                }
            }
        }

        // Set day
        if (await this.elementExists('Day')) {
            const coords = await this.findElementCoordinates('Day');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.birthDay.toString());
            }
        }

        // Set year
        if (await this.elementExists('Year')) {
            const coords = await this.findElementCoordinates('Year');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.humanTypeText(accountData.birthYear.toString());
            }
        }

        // Set gender
        if (await this.elementExists('Gender')) {
            const coords = await this.findElementCoordinates('Gender');
            if (coords) {
                await this.randomTap(coords.x, coords.y);
                await this.randomDelay(500, 1000);

                if (await this.elementExists(accountData.gender)) {
                    const genderCoords = await this.findElementCoordinates(accountData.gender);
                    if (genderCoords) await this.randomTap(genderCoords.x, genderCoords.y);
                }
            }
        }
    }

    // Main account creation function
    async createAccount() {
        const accountData = this.generateAccountData();
        this.log(`Starting account creation for: ${accountData.email}`);

        let retries = 0;
        while (retries < this.maxRetries) {
            try {
                // Randomize device settings
                await this.randomizeDeviceSettings();
                await this.randomDelay(2000, 4000);

                // Launch Gmail
                if (!await this.launchGmail()) {
                    throw new Error('Failed to launch Gmail');
                }

                // Navigate through account creation flow
                await this.navigateAccountCreation(accountData);

                // Save successful account
                await this.saveAccount(accountData);
                this.log(`Successfully created account: ${accountData.email}`, 'SUCCESS');

                return accountData;

            } catch (error) {
                retries++;
                this.log(`Account creation attempt ${retries} failed: ${error.message}`, 'ERROR');

                if (retries >= this.maxRetries) {
                    this.log(`Max retries reached for account creation`, 'ERROR');
                    throw error;
                }

                // Wait before retry
                await this.randomDelay(5000, 10000);
            }
        }
    }

    // Save account data to JSON file
    async saveAccount(accountData) {
        try {
            let accounts = [];

            // Read existing accounts if file exists
            if (await fs.pathExists(this.accountsFile)) {
                const data = await fs.readFile(this.accountsFile, 'utf8');
                accounts = JSON.parse(data);
            }

            // Add new account
            accounts.push(accountData);

            // Write back to file
            await fs.writeFile(this.accountsFile, JSON.stringify(accounts, null, 2));
            this.log(`Account saved to ${this.accountsFile}`);

        } catch (error) {
            this.log(`Error saving account: ${error.message}`, 'ERROR');
            throw error;
        }
    }

    // Initialize device connection
    async initialize() {
        try {
            this.log('Initializing ADB connection...');
            const devices = await this.client.listDevices();

            if (devices.length === 0) {
                throw new Error('No devices connected');
            }

            this.device = this.client.getDevice(devices[0].id);
            this.log(`Connected to device: ${devices[0].id}`);

            // Get screen dimensions
            const displayInfo = await this.device.shell('wm size');
            const match = displayInfo.toString().match(/(\d+)x(\d+)/);
            if (match) {
                this.screenWidth = parseInt(match[1]);
                this.screenHeight = parseInt(match[2]);
                this.log(`Screen dimensions: ${this.screenWidth}x${this.screenHeight}`);
            }

            return true;
        } catch (error) {
            this.log(`Initialization failed: ${error.message}`, 'ERROR');
            throw error;
        }
    }

    // Placeholder for IP rotation (to be implemented by user)
    async rotateIP() {
        this.log('Rotating IP address...');
        // This function should be implemented by the user
        // For now, just add a delay to simulate the process
        await new Promise(resolve => setTimeout(resolve, 5000));
        this.log('IP rotation completed (placeholder)');
    }

    // Cooldown between account creations
    async cooldownPeriod() {
        const cooldownTime = Math.floor(Math.random() * 90000) + 30000; // 30-120 seconds
        this.log(`Starting cooldown period: ${cooldownTime / 1000} seconds`);
        await new Promise(resolve => setTimeout(resolve, cooldownTime));
    }

    // Main execution method
    async run(numberOfAccounts = 1) {
        try {
            await this.initialize();

            for (let i = 0; i < numberOfAccounts; i++) {
                this.log(`\n=== Creating account ${i + 1} of ${numberOfAccounts} ===`);

                try {
                    const account = await this.createAccount();
                    this.log(`Account ${i + 1} created successfully: ${account.email}`);

                    // Rotate IP after successful creation
                    await this.rotateIP();

                    // Cooldown before next account (except for the last one)
                    if (i < numberOfAccounts - 1) {
                        await this.cooldownPeriod();
                    }

                } catch (error) {
                    this.log(`Failed to create account ${i + 1}: ${error.message}`, 'ERROR');
                    // Continue with next account
                }
            }

            this.log(`\n=== Batch completed. Created accounts saved to ${this.accountsFile} ===`);

        } catch (error) {
            this.log(`Fatal error: ${error.message}`, 'ERROR');
            throw error;
        }
    }
}

// Export the class and utility functions
module.exports = {
    GmailAccountCreator,

    // Utility functions for external use
    randomDelay: async (min = 500, max = 2500) => {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        await new Promise(resolve => setTimeout(resolve, delay));
    },

    randomTap: async (device, x, y, variation = 10, screenWidth = 1080, screenHeight = 1920) => {
        const randomX = x + (Math.random() - 0.5) * variation * 2;
        const randomY = y + (Math.random() - 0.5) * variation * 2;

        const finalX = Math.max(0, Math.min(screenWidth, Math.floor(randomX)));
        const finalY = Math.max(0, Math.min(screenHeight, Math.floor(randomY)));

        await device.shell(`input tap ${finalX} ${finalY}`);
    },

    rotateIP: async () => {
        // Placeholder function - implement your IP rotation logic here
        console.log('IP rotation function called - implement your logic here');
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
};

// If running directly, execute the script
if (require.main === module) {
    const creator = new GmailAccountCreator();

    // Get number of accounts from command line argument or default to 1
    const numberOfAccounts = parseInt(process.argv[2]) || 1;

    creator.run(numberOfAccounts)
        .then(() => {
            console.log('Script completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('Script failed:', error.message);
            process.exit(1);
        });
}
