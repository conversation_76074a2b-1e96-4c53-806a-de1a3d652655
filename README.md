# Gmail Account Creator

An automated Google account creation tool using ADB and devicefarmer/adbkit for Android devices.

## Features

- **Automated Gmail account creation** via ADB commands
- **Anti-detection strategies** including:
  - Human-like behavior with random delays and coordinate variations
  - Device fingerprint randomization (language, timezone, display density)
  - Keyboard simulation with typos and corrections
  - Flow randomization for account creation steps
- **Error handling and retries** with graceful fallbacks
- **State verification** using UI dumps before actions
- **Comprehensive logging** to `run.log` file
- **Account data storage** in `accounts.json` with append-safe operations
- **IP rotation support** (placeholder for custom implementation)
- **Cooldown periods** between account creations

## Prerequisites

1. **Android device** connected via USB with USB debugging enabled
2. **ADB** installed and device authorized
3. **Node.js** (v12 or higher)
4. **Gmail app** installed on the Android device

## Installation

```bash
pnpm install adbkit fs-extra
```

Or if you prefer npm:
```bash
npm install adbkit fs-extra
```

## Quick Start

```bash
# 1. Install dependencies
pnpm install

# 2. Test your device connection
pnpm run test-connection

# 3. <PERSON>reate accounts
pnpm start  # Creates 1 account
# or
node gmail-account-creator.js 5  # Creates 5 accounts
```

## Usage

### Basic Usage

```javascript
const { GmailAccountCreator } = require('./gmail-account-creator');

const creator = new GmailAccountCreator();
creator.run(3); // Create 3 accounts
```

### Command Line Usage

```bash
# Test device connection first
pnpm run test-connection
# or: node test-connection.js

# Create 1 account (default)
pnpm start
# or: node gmail-account-creator.js

# Create 5 accounts
node gmail-account-creator.js 5

# Run example with custom IP rotation
pnpm run example
# or: node example-usage.js
```

### Custom IP Rotation

Implement your own `rotateIP()` function:

```javascript
const creator = new GmailAccountCreator();

creator.rotateIP = async function() {
    // Your custom IP rotation logic here
    // e.g., VPN switching, proxy rotation, etc.
    console.log('Switching to new IP...');
    await switchVPN();
    await waitForIPChange();
};

await creator.run(3);
```

## Configuration

The script includes several configurable parameters:

- **Delays**: Random delays between 500ms-2500ms for human-like behavior
- **Retries**: Maximum 3 retry attempts per account
- **Cooldown**: 30-120 seconds between account creations
- **Coordinate variation**: ±10 pixels for tap positions
- **Screen dimensions**: Auto-detected from device

## Generated Account Data

Each account includes:

```json
{
  "firstName": "Alex",
  "lastName": "Smith",
  "username": "alexsmith1234",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "birthYear": 1995,
  "birthMonth": 6,
  "birthDay": 15,
  "gender": "Male",
  "recoveryEmail": "<EMAIL>",
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

## Anti-Detection Features

1. **Human-like Behavior**:
   - Random delays between actions
   - Coordinate variation for taps
   - Simulated typos and corrections

2. **Device Fingerprint Variation**:
   - Random language settings
   - Random timezone changes
   - Display density modifications

3. **Flow Randomization**:
   - Random order for optional fields
   - Skip/fill recovery information randomly
   - Varied birthdates and demographics

4. **Error Handling**:
   - UI state verification before actions
   - Graceful retries on failures
   - Comprehensive error logging

## Logging

All actions are logged to `run.log` with timestamps:

```
[2024-01-15T10:30:00.000Z] [INFO] Starting account creation for: <EMAIL>
[2024-01-15T10:30:01.500Z] [INFO] Randomizing device settings for anti-detection
[2024-01-15T10:30:05.200Z] [INFO] Launching Gmail app
[2024-01-15T10:30:08.100Z] [INFO] Tapping at coordinates (540, 960)
```

## Important Notes

- **Legal Compliance**: Ensure you comply with Google's Terms of Service and applicable laws
- **Rate Limiting**: Google has sophisticated detection systems - use reasonable delays
- **Device Security**: Only use on devices you own or have explicit permission to use
- **IP Rotation**: Implement proper IP rotation to avoid detection
- **Account Verification**: Some accounts may require phone verification

## Troubleshooting

1. **Device not found**: Ensure USB debugging is enabled and device is authorized
2. **App crashes**: Check if Gmail app is updated and compatible
3. **UI elements not found**: Screen resolution or app version differences may require coordinate adjustments
4. **Account creation fails**: Google's anti-bot measures may block creation - try different IP/device settings

## License

This tool is for educational purposes only. Use responsibly and in compliance with all applicable terms of service and laws.
