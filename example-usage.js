const { GmailAccountCreator } = require('./gmail-account-creator');

// Example usage of the Gmail Account Creator

async function main() {
    const creator = new GmailAccountCreator();
    
    try {
        console.log('Starting Gmail account creation automation...');
        
        // Create 3 accounts
        await creator.run(3);
        
        console.log('All accounts created successfully!');
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Custom IP rotation function example
async function customRotateIP() {
    console.log('Implementing custom IP rotation...');
    
    // Example: You could implement VPN switching, proxy rotation, etc.
    // For demonstration, we'll just simulate the process
    
    // Simulate VPN connection change
    console.log('Switching VPN server...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Simulate waiting for new IP
    console.log('Waiting for IP change confirmation...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('IP rotation completed');
}

// Override the rotateIP function with your custom implementation
const originalCreator = new GmailAccountCreator();
originalCreator.rotateIP = customRotateIP;

// Run with custom IP rotation
async function runWithCustomIPRotation() {
    try {
        await originalCreator.run(2);
    } catch (error) {
        console.error('Error with custom IP rotation:', error.message);
    }
}

// Uncomment the function you want to run:
main();
// runWithCustomIPRotation();
