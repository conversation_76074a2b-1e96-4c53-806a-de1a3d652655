const adb = require('adbkit');

// Simple test script to verify ADB connection and device setup

async function testConnection() {
    console.log('Testing ADB connection...');
    
    try {
        const client = adb.createClient();
        const devices = await client.listDevices();
        
        if (devices.length === 0) {
            console.error('❌ No devices found. Please ensure:');
            console.error('   1. Android device is connected via USB');
            console.error('   2. USB debugging is enabled');
            console.error('   3. Device is authorized for debugging');
            return false;
        }
        
        console.log(`✅ Found ${devices.length} device(s):`);
        devices.forEach((device, index) => {
            console.log(`   ${index + 1}. ${device.id} (${device.type})`);
        });
        
        // Test connection to first device
        const device = client.getDevice(devices[0].id);
        
        // Get device info
        console.log('\n📱 Device Information:');
        
        try {
            const model = await device.shell('getprop ro.product.model');
            console.log(`   Model: ${model.toString().trim()}`);
        } catch (e) {
            console.log('   Model: Unable to retrieve');
        }
        
        try {
            const version = await device.shell('getprop ro.build.version.release');
            console.log(`   Android Version: ${version.toString().trim()}`);
        } catch (e) {
            console.log('   Android Version: Unable to retrieve');
        }
        
        try {
            const screenSize = await device.shell('wm size');
            console.log(`   Screen Size: ${screenSize.toString().trim()}`);
        } catch (e) {
            console.log('   Screen Size: Unable to retrieve');
        }
        
        // Test Gmail app
        console.log('\n📧 Gmail App Check:');
        try {
            const gmailCheck = await device.shell('pm list packages | grep com.google.android.gm');
            if (gmailCheck.toString().includes('com.google.android.gm')) {
                console.log('   ✅ Gmail app is installed');
            } else {
                console.log('   ❌ Gmail app not found - please install Gmail');
                return false;
            }
        } catch (e) {
            console.log('   ❌ Unable to check Gmail installation');
            return false;
        }
        
        // Test UI dump capability
        console.log('\n🔍 UI Automation Test:');
        try {
            await device.shell('uiautomator dump /sdcard/test_dump.xml');
            console.log('   ✅ UI dump capability working');
            
            // Clean up test file
            await device.shell('rm /sdcard/test_dump.xml');
        } catch (e) {
            console.log('   ❌ UI dump failed - automation may not work properly');
            console.log(`   Error: ${e.message}`);
        }
        
        console.log('\n🎉 Connection test completed successfully!');
        console.log('Your device is ready for Gmail account automation.');
        
        return true;
        
    } catch (error) {
        console.error('❌ Connection test failed:', error.message);
        console.error('\nTroubleshooting tips:');
        console.error('1. Make sure ADB is installed and in your PATH');
        console.error('2. Enable Developer Options on your Android device');
        console.error('3. Enable USB Debugging in Developer Options');
        console.error('4. Accept the USB debugging authorization dialog');
        console.error('5. Try running: adb devices');
        
        return false;
    }
}

// Run the test
if (require.main === module) {
    testConnection()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = { testConnection };
